import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { ArrowLeft, ArrowRight } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { useScanStore } from '@/hooks/useScanStore';
import ResultCard from '@/components/ResultCard';
import EmptyState from '@/components/EmptyState';

export default function ResultScreen() {
  const router = useRouter();
  const { currentScan, toggleSaved } = useScanStore();
  
  console.log('ResultScreen - currentScan:', currentScan);

  const handleBack = () => {
    router.back();
  };

  const handleViewDetails = () => {
    if (currentScan) {
      router.push(`/details/${currentScan.id}`);
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Scan Result',
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.headerButton}>
              <ArrowLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <View style={styles.container}>
        {currentScan ? (
          <View style={styles.resultContainer}>
            <ResultCard 
              result={currentScan} 
              onToggleSave={toggleSaved} 
            />
            
            <TouchableOpacity 
              style={styles.detailsButton}
              onPress={handleViewDetails}
            >
              <ArrowRight size={20} color={Colors.card} />
            </TouchableOpacity>
          </View>
        ) : (
          <EmptyState 
            title="No scan result"
            message="Return to the scanner to identify an object"
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerButton: {
    padding: 8,
  },
  resultContainer: {
    flex: 1,
    paddingTop: 16,
  },
  detailsButton: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});