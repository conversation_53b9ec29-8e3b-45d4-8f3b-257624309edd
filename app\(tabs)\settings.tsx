import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Switch, ScrollView, Linking, Alert, Share } from 'react-native';
import { ChevronRight, ExternalLink, HelpCircle, Info, Lock, Share2, Star, Trash2 } from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '@/constants/colors';
import { useScanStore } from '@/hooks/useScanStore';

export default function SettingsScreen() {
  const { history } = useScanStore();
  const [notificationsEnabled, setNotificationsEnabled] = React.useState(false);
  // const [saveHistory, setSaveHistory] = React.useState(true);
  
  const clearHistory = async () => {
    Alert.alert(
      'Clear History',
      'Are you sure you want to clear your scan history? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('whats-that-scan-history');
              Alert.alert('Success', 'Your scan history has been cleared.');
            } catch (error) {
              console.error('Failed to clear history:', error);
              Alert.alert('Error', 'Failed to clear history. Please try again.');
            }
          },
        },
      ]
    );
  };

  const renderSettingItem = (
    icon: React.ReactNode,
    title: string,
    subtitle?: string,
    rightElement?: React.ReactNode,
    onPress?: () => void,
  ) => (
    <TouchableOpacity 
      style={styles.settingItem} 
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingIcon}>{icon}</View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightElement || <ChevronRight size={20} color={Colors.inactive} />}
    </TouchableOpacity>
  );

  const renderSectionHeader = (title: string) => (
    <Text style={styles.sectionHeader}>{title}</Text>
  );

  const renderSeparator = () => <View style={styles.separator} />;

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        {renderSectionHeader('App Settings')}
        
        {renderSettingItem(
          <Info size={22} color={Colors.primary} />,
          'Notifications',
          'Receive updates about new features',
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: Colors.border, true: Colors.primary }}
            thumbColor={Colors.card}
          />
        )}
        
        {renderSeparator()}
        
        {renderSettingItem(
          <Lock size={22} color={Colors.primary} />,
          'Privacy',
          'Manage your data and privacy settings',
          undefined,
          () => {}
        )}
        
        {renderSeparator()}
        
        {renderSettingItem(
          <Trash2 size={22} color={Colors.error} />,
          'Clear History',
          `${history.length} items in your history`,
          undefined,
          clearHistory
        )}
      </View>
      
      <View style={styles.section}>
        {renderSectionHeader('About')}
        
        {renderSettingItem(
          <Star size={22} color={Colors.warning} />,
          'Rate the App',
          'Let us know how we\'re doing',
          <ExternalLink size={20} color={Colors.inactive} />,
          () => Linking.openURL('https://apps.apple.com')
        )}
        
        {renderSeparator()}
        
        {renderSettingItem(
          <Share2 size={22} color={Colors.accent} />,
          'Share with Friends',
          'Spread the word about What\'s That?',
          undefined,
          () => {
            Share.share({
              message: 'Check out "What\'s That?" - an app that identifies anything with your camera! https://whatsthat.app',
            });
          }
        )}
        
        {renderSeparator()}
        
        {renderSettingItem(
          <HelpCircle size={22} color={Colors.primary} />,
          'Help & Support',
          'Get assistance or report an issue',
          undefined,
          () => {}
        )}
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.version}>What&apos;s That? v1.0.0</Text>
        <Text style={styles.copyright}>© 2025 What&apos;s That Inc.</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  section: {
    backgroundColor: Colors.card,
    marginBottom: 20,
    paddingVertical: 8,
    borderRadius: 12,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginHorizontal: 16,
    marginVertical: 8,
    textTransform: 'uppercase',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
    marginHorizontal: 16,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
    marginBottom: 20,
  },
  version: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  copyright: {
    fontSize: 12,
    color: Colors.inactive,
  },
});