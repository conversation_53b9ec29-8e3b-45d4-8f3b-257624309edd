import React from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import { Colors } from '@/constants/colors';

const { width } = Dimensions.get('window');
const FRAME_WIDTH = width * 0.7;
const FRAME_HEIGHT = FRAME_WIDTH;
const CORNER_SIZE = 30;
const CORNER_THICKNESS = 4;

const ScanFrame: React.FC = () => {
  return (
    <View style={styles.container} testID="scan-frame">
      <View style={styles.frame}>
        {/* Top Left Corner */}
        <View style={[styles.corner, styles.topLeft]}>
          <View style={[styles.cornerBorder, styles.cornerBorderTop, styles.cornerBorderLeft]} />
        </View>
        
        {/* Top Right Corner */}
        <View style={[styles.corner, styles.topRight]}>
          <View style={[styles.cornerBorder, styles.cornerBorderTop, styles.cornerBorderRight]} />
        </View>
        
        {/* Bottom Left Corner */}
        <View style={[styles.corner, styles.bottomLeft]}>
          <View style={[styles.cornerBorder, styles.cornerBorderBottom, styles.cornerBorderLeft]} />
        </View>
        
        {/* Bottom Right Corner */}
        <View style={[styles.corner, styles.bottomRight]}>
          <View style={[styles.cornerBorder, styles.cornerBorderBottom, styles.cornerBorderRight]} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  frame: {
    width: FRAME_WIDTH,
    height: FRAME_HEIGHT,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: CORNER_SIZE,
    height: CORNER_SIZE,
  },
  topLeft: {
    top: 0,
    left: 0,
  },
  topRight: {
    top: 0,
    right: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
  },
  cornerBorder: {
    position: 'absolute',
    backgroundColor: Colors.primary,
  },
  cornerBorderTop: {
    top: 0,
    height: CORNER_THICKNESS,
    width: CORNER_SIZE,
  },
  cornerBorderBottom: {
    bottom: 0,
    height: CORNER_THICKNESS,
    width: CORNER_SIZE,
  },
  cornerBorderLeft: {
    left: 0,
    width: CORNER_THICKNESS,
    height: CORNER_SIZE,
  },
  cornerBorderRight: {
    right: 0,
    width: CORNER_THICKNESS,
    height: CORNER_SIZE,
  },
});

export default ScanFrame;