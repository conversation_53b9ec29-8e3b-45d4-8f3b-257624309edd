import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Alert } from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { Image, Upload } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { Colors } from '@/constants/colors';
import ScanButton from '@/components/ScanButton';
import ScanFrame from '@/components/ScanFrame';
import { useScanStore } from '@/hooks/useScanStore';

export default function ScanScreen() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef<any>(null);
  const router = useRouter();
  
  const { processImage, isProcessing } = useScanStore();

  useEffect(() => {
    requestPermission();
  }, [requestPermission]);

  const handleCapture = async () => {
    if (isProcessing) return;
    
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePictureAsync();
        await handleImageProcessing(photo.uri);
      } catch (error) {
        console.error('Error taking picture:', error);
        Alert.alert('Error', 'Failed to capture image. Please try again.');
      }
    }
  };

  const handlePickImage = async () => {
    if (isProcessing) return;
    
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      await handleImageProcessing(result.assets[0].uri);
    }
  };

  const handleImageProcessing = async (imageUri: string) => {
    try {
      console.log('Starting image processing for:', imageUri);
      const result = await processImage(imageUri);
      console.log('Image processing completed, result:', result);
      router.push('/result');
    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert('Error', 'Failed to process image. Please try again.');
    }
  };

  const toggleCameraFacing = () => {
    setFacing((current: CameraType) => (current === 'back' ? 'front' : 'back'));
  };

  if (!permission) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading camera...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.permissionText}>We need your permission to use the camera</Text>
        <TouchableOpacity 
          style={styles.permissionButton} 
          onPress={requestPermission}
        >
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={facing}
        testID="camera-view"
      >
        <ScanFrame />
        
        <View style={styles.controls}>
          <TouchableOpacity 
            style={styles.controlButton} 
            onPress={toggleCameraFacing}
            disabled={isProcessing}
          >
            <Image size={24} color={Colors.card} />
          </TouchableOpacity>
          
          <ScanButton 
            onPress={handleCapture} 
            isProcessing={isProcessing}
            isScanMode={true}
          />
          
          <TouchableOpacity 
            style={styles.controlButton} 
            onPress={handlePickImage}
            disabled={isProcessing}
          >
            <Upload size={24} color={Colors.card} />
          </TouchableOpacity>
        </View>
      </CameraView>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Point camera at any object to identify it
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  camera: {
    flex: 1,
  },
  controls: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer: {
    backgroundColor: Colors.card,
    padding: 16,
    alignItems: 'center',
  },
  footerText: {
    color: Colors.textSecondary,
    fontSize: 14,
  },
  loadingText: {
    fontSize: 18,
    color: Colors.text,
    textAlign: 'center',
    marginTop: 20,
  },
  permissionText: {
    fontSize: 18,
    color: Colors.text,
    textAlign: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: Colors.card,
    fontSize: 16,
    fontWeight: '600',
  },
});