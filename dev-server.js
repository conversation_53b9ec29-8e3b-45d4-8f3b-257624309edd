const http = require('http');
const os = require('os');
const qrcode = require('qrcode');

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();
const port = 19000;
const metroPort = 8081;

// Generate QR codes
const urls = {
  exp: `exp://${localIP}:${metroPort}`,
  http: `http://${localIP}:${metroPort}`
};

const server = http.createServer(async (req, res) => {
  try {
    const expQR = await qrcode.toString(urls.exp, { type: 'svg', width: 300 });
    const httpQR = await qrcode.toString(urls.http, { type: 'svg', width: 300 });
    
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>📱 Your Main Project - Expo Development Server</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; max-width: 1000px; margin: 30px auto; padding: 30px; background: #f8f9fa; }
          .header { text-align: center; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
          .qr-section { display: flex; gap: 30px; margin: 30px 0; }
          .qr-card { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); flex: 1; text-align: center; }
          .metro-info { background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #28a745; }
          .connection-info { background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #ffc107; }
          code { background: #f1f3f4; padding: 4px 8px; border-radius: 4px; font-family: 'Monaco', 'Menlo', monospace; }
          .url-list { list-style: none; padding: 0; }
          .url-list li { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
          h1 { color: #333; margin: 0 0 10px 0; }
          h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
          h3 { color: #6c757d; margin-top: 25px; }
          .status { position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 10px 15px; border-radius: 20px; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="status">✅ Metro Running</div>
        
        <div class="header">
          <h1>📱 Your Main Project is Running!</h1>
          <p style="color: #6c757d; font-size: 18px;">Metro bundler is active on your real project</p>
        </div>

        <div class="metro-info">
          <h3>✅ Metro Bundler Status</h3>
          <p><strong>Your main project</strong> is now running with Metro on:</p>
          <ul>
            <li><strong>Port:</strong> ${metroPort}</li>
            <li><strong>IP Address:</strong> ${localIP}</li>
            <li><strong>Project Path:</strong> whatsthatmobileapp (your real project)</li>
          </ul>
        </div>

        <div class="qr-section">
          <div class="qr-card">
            <h3>📱 Expo Go Connection</h3>
            ${expQR}
            <p><strong>Expo URL:</strong><br/><code>${urls.exp}</code></p>
            <p style="color: #6c757d; font-size: 14px;">Scan with Expo Go app</p>
          </div>
          
          <div class="qr-card">
            <h3>🌐 HTTP Connection</h3>
            ${httpQR}
            <p><strong>HTTP URL:</strong><br/><code>${urls.http}</code></p>
            <p style="color: #6c757d; font-size: 14px;">Alternative connection method</p>
          </div>
        </div>

        <div class="connection-info">
          <h3>📲 How to Connect Your Phone:</h3>
          <ol style="line-height: 1.8;">
            <li><strong>Download Expo Go</strong> from your app store</li>
            <li><strong>Ensure same WiFi:</strong> Your phone and computer must be on the same network</li>
            <li><strong>Scan QR code</strong> above with Expo Go, or</li>
            <li><strong>Manual entry:</strong> Type the URL manually in Expo Go</li>
          </ol>
          
          <h4>📍 Connection URLs for Manual Entry:</h4>
          <ul class="url-list">
            <li><strong>Primary (Expo):</strong> <code>${urls.exp}</code></li>
            <li><strong>Alternative (HTTP):</strong> <code>${urls.http}</code></li>
            <li><strong>Local IP:</strong> <code>${localIP}</code></li>
          </ul>
        </div>

        <div style="background: white; padding: 20px; border-radius: 10px; margin-top: 30px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h3>🎉 Success!</h3>
          <p>Your main project (whatsthatmobileapp) is now running properly with Metro bundler.</p>
          <p>No more temporary projects - this is your real app!</p>
          <p><small>Last updated: <span id="status">${new Date().toLocaleTimeString()}</span></small></p>
        </div>

        <script>
          setInterval(() => {
            document.getElementById('status').textContent = new Date().toLocaleTimeString();
          }, 1000);
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    res.writeHead(500, { 'Content-Type': 'text/plain' });
    res.end('Error generating QR codes: ' + error.message);
  }
});

server.listen(port, () => {
  console.log(`🎉 SUCCESS! Your main project development server is running!`);
  console.log(`📱 Open in browser: http://localhost:${port}`);
  console.log(`📲 Expo Go URL: ${urls.exp}`);
  console.log(`🌐 HTTP URL: ${urls.http}`);
  console.log(`🚀 Metro running on port ${metroPort}`);
});