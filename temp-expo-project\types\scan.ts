export type ItemCategory = 'product' | 'plant' | 'animal' | 'landmark' | 'unknown';

export interface ScanResult {
  id: string;
  name: string;
  category: ItemCategory;
  description: string;
  confidence: number;
  imageUri: string;
  timestamp: number;
  saved: boolean;
}

export interface ProductDetails extends ScanResult {
  price?: string;
  rating?: number;
  affiliateLinks?: {
    amazon?: string;
    walmart?: string;
    ebay?: string;
  };
}

export interface PlantDetails extends ScanResult {
  scientificName?: string;
  careInfo?: string;
  toxicity?: string;
}

export interface AnimalDetails extends ScanResult {
  species?: string;
  breed?: string;
  behavior?: string;
}

export interface LandmarkDetails extends ScanResult {
  location?: string;
  history?: string;
  nearbyInfo?: string;
}