import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Linking } from 'react-native';
import { Image } from 'expo-image';
import { Bookmark, BookmarkCheck, ExternalLink, Share2, Star } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { AnimalDetails, ItemCategory, LandmarkDetails, PlantDetails, ProductDetails } from '@/types/scan';

interface DetailedResultProps {
  result: ProductDetails | PlantDetails | AnimalDetails | LandmarkDetails;
  onToggleSave: (id: string) => void;
}

const DetailedResult: React.FC<DetailedResultProps> = ({ result, onToggleSave }) => {
  const { id, name, category, description, confidence, imageUri, saved } = result;
  
  const confidencePercent = Math.round(confidence * 100);
  
  const getCategoryColor = (category: ItemCategory) => {
    switch (category) {
      case 'product': return Colors.primary;
      case 'plant': return Colors.success;
      case 'animal': return Colors.warning;
      case 'landmark': return Colors.accent;
      default: return Colors.textSecondary;
    }
  };

  const categoryColor = getCategoryColor(category);

  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating - fullStars >= 0.5;
    const stars = [];

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<Star key={i} size={16} fill={Colors.warning} color={Colors.warning} />);
      } else if (i === fullStars && hasHalfStar) {
        // For simplicity, we'll just use a full star for half stars
        stars.push(<Star key={i} size={16} fill={Colors.warning} color={Colors.warning} />);
      } else {
        stars.push(<Star key={i} size={16} color={Colors.inactive} />);
      }
    }

    return stars;
  };

  const renderProductDetails = (product: ProductDetails) => (
    <View style={styles.detailsSection}>
      <View style={styles.priceRatingRow}>
        {product.price && (
          <Text style={styles.price}>{product.price}</Text>
        )}
        {product.rating && (
          <View style={styles.ratingContainer}>
            <View style={styles.stars}>
              {renderStars(product.rating)}
            </View>
            <Text style={styles.ratingText}>{product.rating.toFixed(1)}</Text>
          </View>
        )}
      </View>
      
      {product.affiliateLinks && (
        <View style={styles.affiliateLinks}>
          <Text style={styles.sectionTitle}>Buy from:</Text>
          
          {product.affiliateLinks.amazon && (
            <TouchableOpacity 
              style={[styles.affiliateButton, { backgroundColor: '#FF9900' }]}
              onPress={() => Linking.openURL(product.affiliateLinks!.amazon!)}
            >
              <Text style={styles.affiliateButtonText}>Amazon</Text>
              <ExternalLink size={16} color={Colors.card} />
            </TouchableOpacity>
          )}
          
          {product.affiliateLinks.walmart && (
            <TouchableOpacity 
              style={[styles.affiliateButton, { backgroundColor: '#0071DC' }]}
              onPress={() => Linking.openURL(product.affiliateLinks!.walmart!)}
            >
              <Text style={styles.affiliateButtonText}>Walmart</Text>
              <ExternalLink size={16} color={Colors.card} />
            </TouchableOpacity>
          )}
          
          {product.affiliateLinks.ebay && (
            <TouchableOpacity 
              style={[styles.affiliateButton, { backgroundColor: '#E53238' }]}
              onPress={() => Linking.openURL(product.affiliateLinks!.ebay!)}
            >
              <Text style={styles.affiliateButtonText}>eBay</Text>
              <ExternalLink size={16} color={Colors.card} />
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );

  const renderPlantDetails = (plant: PlantDetails) => (
    <View style={styles.detailsSection}>
      {plant.scientificName && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Scientific Name:</Text>
          <Text style={styles.detailValue}>{plant.scientificName}</Text>
        </View>
      )}
      
      {plant.careInfo && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Care Info:</Text>
          <Text style={styles.detailValue}>{plant.careInfo}</Text>
        </View>
      )}
      
      {plant.toxicity && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Toxicity:</Text>
          <Text style={[
            styles.detailValue, 
            { color: plant.toxicity.includes('Non-toxic') ? Colors.success : Colors.error }
          ]}>
            {plant.toxicity}
          </Text>
        </View>
      )}
    </View>
  );

  const renderAnimalDetails = (animal: AnimalDetails) => (
    <View style={styles.detailsSection}>
      {animal.species && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Species:</Text>
          <Text style={styles.detailValue}>{animal.species}</Text>
        </View>
      )}
      
      {animal.breed && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Breed:</Text>
          <Text style={styles.detailValue}>{animal.breed}</Text>
        </View>
      )}
      
      {animal.behavior && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Behavior:</Text>
          <Text style={styles.detailValue}>{animal.behavior}</Text>
        </View>
      )}
    </View>
  );

  const renderLandmarkDetails = (landmark: LandmarkDetails) => (
    <View style={styles.detailsSection}>
      {landmark.location && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Location:</Text>
          <Text style={styles.detailValue}>{landmark.location}</Text>
        </View>
      )}
      
      {landmark.history && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>History:</Text>
          <Text style={styles.detailValue}>{landmark.history}</Text>
        </View>
      )}
      
      {landmark.nearbyInfo && (
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Nearby:</Text>
          <Text style={styles.detailValue}>{landmark.nearbyInfo}</Text>
        </View>
      )}
    </View>
  );

  const renderCategorySpecificDetails = () => {
    switch (category) {
      case 'product':
        return renderProductDetails(result as ProductDetails);
      case 'plant':
        return renderPlantDetails(result as PlantDetails);
      case 'animal':
        return renderAnimalDetails(result as AnimalDetails);
      case 'landmark':
        return renderLandmarkDetails(result as LandmarkDetails);
      default:
        return null;
    }
  };

  return (
    <ScrollView style={styles.container} testID={`detailed-result-${id}`}>
      <Image 
        source={{ uri: imageUri }}
        style={styles.image}
        contentFit="cover"
        transition={300}
      />
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{name}</Text>
          <View style={[styles.categoryBadge, { backgroundColor: categoryColor }]}>
            <Text style={styles.categoryText}>{category}</Text>
          </View>
        </View>
        
        <View style={styles.confidenceBar}>
          <View 
            style={[
              styles.confidenceFill, 
              { width: `${confidencePercent}%`, backgroundColor: categoryColor }
            ]} 
          />
          <Text style={styles.confidenceLabel}>
            Confidence: <Text style={styles.confidenceValue}>{confidencePercent}%</Text>
          </Text>
        </View>
        
        <Text style={styles.description}>{description}</Text>
        
        {renderCategorySpecificDetails()}
        
        <View style={styles.actions}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.saveButton]} 
            onPress={() => onToggleSave(id)}
          >
            {saved ? (
              <BookmarkCheck size={20} color={Colors.primary} />
            ) : (
              <Bookmark size={20} color={Colors.primary} />
            )}
            <Text style={styles.actionText}>{saved ? 'Saved' : 'Save'}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, styles.shareButton]}>
            <Share2 size={20} color={Colors.accent} />
            <Text style={[styles.actionText, { color: Colors.accent }]}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  image: {
    width: '100%',
    height: 250,
  },
  content: {
    padding: 16,
    backgroundColor: Colors.card,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    flex: 1,
  },
  categoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginLeft: 8,
  },
  categoryText: {
    color: Colors.card,
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  confidenceBar: {
    height: 24,
    backgroundColor: Colors.border,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    position: 'relative',
  },
  confidenceFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    borderRadius: 12,
  },
  confidenceLabel: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text,
  },
  confidenceValue: {
    fontWeight: '700',
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 20,
  },
  detailsSection: {
    marginBottom: 20,
  },
  priceRatingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  price: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stars: {
    flexDirection: 'row',
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  affiliateLinks: {
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  affiliateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  affiliateButtonText: {
    color: Colors.card,
    fontWeight: '600',
    marginRight: 8,
  },
  detailRow: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: Colors.text,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  saveButton: {
    backgroundColor: Colors.highlight,
    marginRight: 8,
  },
  shareButton: {
    backgroundColor: '#F5F3FF',
    marginLeft: 8,
  },
  actionText: {
    marginLeft: 8,
    fontWeight: '500',
    color: Colors.primary,
  },
});

export default DetailedResult;