import React, { useState } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity, Text } from 'react-native';
import { useRouter } from 'expo-router';
import { Bookmark, Filter } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { useScanStore } from '@/hooks/useScanStore';
import ResultCard from '@/components/ResultCard';
import EmptyState from '@/components/EmptyState';
import { ItemCategory } from '@/types/scan';

export default function HistoryScreen() {
  const router = useRouter();
  const { history, toggleSaved } = useScanStore();
  const [activeFilter, setActiveFilter] = useState<ItemCategory | 'all'>('all');
  const [savedOnly, setSavedOnly] = useState(false);
  
  const filteredHistory = history.filter(item => {
    if (savedOnly && !item.saved) return false;
    if (activeFilter !== 'all' && item.category !== activeFilter) return false;
    return true;
  });

  const handleCardPress = (id: string) => {
    router.push(`/details/${id}`);
  };

  const renderFilterChip = (label: string, value: ItemCategory | 'all') => {
    const isActive = activeFilter === value;
    
    return (
      <TouchableOpacity
        style={[
          styles.filterChip,
          isActive && { backgroundColor: getCategoryColor(value) }
        ]}
        onPress={() => setActiveFilter(value)}
      >
        <Text 
          style={[
            styles.filterChipText,
            isActive && { color: Colors.card }
          ]}
        >
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  const getCategoryColor = (category: ItemCategory | 'all'): string => {
    switch (category) {
      case 'product': return Colors.primary;
      case 'plant': return Colors.success;
      case 'animal': return Colors.warning;
      case 'landmark': return Colors.accent;
      case 'all': return Colors.text;
      default: return Colors.textSecondary;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.filtersContainer}>
        <View style={styles.filterChips}>
          {renderFilterChip('All', 'all')}
          {renderFilterChip('Products', 'product')}
          {renderFilterChip('Plants', 'plant')}
          {renderFilterChip('Animals', 'animal')}
          {renderFilterChip('Landmarks', 'landmark')}
        </View>
        
        <TouchableOpacity
          style={[
            styles.savedFilter,
            savedOnly && { backgroundColor: Colors.highlight }
          ]}
          onPress={() => setSavedOnly(!savedOnly)}
        >
          <Bookmark 
            size={18} 
            color={savedOnly ? Colors.primary : Colors.inactive} 
            fill={savedOnly ? Colors.primary : 'transparent'} 
          />
          <Text 
            style={[
              styles.savedFilterText,
              savedOnly && { color: Colors.primary }
            ]}
          >
            Saved
          </Text>
        </TouchableOpacity>
      </View>
      
      {filteredHistory.length === 0 ? (
        <EmptyState 
          title="No items found"
          message={savedOnly 
            ? "You haven't saved any items yet. Tap the bookmark icon on a scan result to save it."
            : "Your scan history will appear here. Start scanning to build your history."}
          icon={<Filter size={64} color={Colors.inactive} />}
        />
      ) : (
        <FlatList
          data={filteredHistory}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ResultCard
              result={item}
              onToggleSave={toggleSaved}
              onPress={() => handleCardPress(item.id)}
              compact
            />
          )}
          contentContainerStyle={styles.listContent}
          testID="history-list"
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  filtersContainer: {
    backgroundColor: Colors.card,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: Colors.border,
    marginRight: 8,
    marginBottom: 8,
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
  },
  savedFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  savedFilterText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  listContent: {
    paddingVertical: 8,
  },
});