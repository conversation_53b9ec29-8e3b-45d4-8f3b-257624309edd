import { Tabs } from "expo-router";
import { History, Home, Settings } from "lucide-react-native";
import React from "react";
import { ScanProvider } from "@/hooks/useScanStore";
import { Colors } from "@/constants/colors";

export default function TabLayout() {
  return (
    <ScanProvider>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: Colors.primary,
          tabBarInactiveTintColor: Colors.inactive,
          tabBarStyle: {
            borderTopWidth: 1,
            borderTopColor: Colors.border,
          },
          headerStyle: {
            backgroundColor: Colors.card,
          },
          headerTitleStyle: {
            fontWeight: '600',
            color: Colors.text,
          },
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: "What's That?",
            tabBarLabel: "Scan",
            tabBarIcon: ({ color }) => <Home size={24} color={color} />,
          }}
        />
        <Tabs.Screen
          name="history"
          options={{
            title: "History",
            tabBarIcon: ({ color }) => <History size={24} color={color} />,
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: "Settings",
            tabBarIcon: ({ color }) => <Settings size={24} color={color} />,
          }}
        />
      </Tabs>
    </ScanProvider>
  );
}