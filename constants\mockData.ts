import { AnimalDetails, ItemCategory, LandmarkDetails, PlantDetails, ProductDetails, ScanResult } from "@/types/scan";

export const generateMockResult = (imageUri: string): ScanResult => {
  const categories: ItemCategory[] = ['product', 'plant', 'animal', 'landmark'];
  const randomCategory = categories[Math.floor(Math.random() * categories.length)];
  
  const baseResult: ScanResult = {
    id: Math.random().toString(36).substring(2, 10),
    name: getMockName(randomCategory),
    category: randomCategory,
    description: getMockDescription(randomCategory),
    confidence: Math.random() * 0.3 + 0.7, // Between 70% and 100%
    imageUri,
    timestamp: Date.now(),
    saved: false
  };

  return baseResult;
};

export const getDetailedResult = (result: ScanResult): ProductDetails | PlantDetails | AnimalDetails | LandmarkDetails => {
  switch (result.category) {
    case 'product':
      return {
        ...result,
        price: `$${(Math.random() * 100 + 10).toFixed(2)}`,
        rating: Math.random() * 3 + 2, // Between 2 and 5
        affiliateLinks: {
          amazon: 'https://amazon.com',
          walmart: 'https://walmart.com',
          ebay: 'https://ebay.com'
        }
      };
    case 'plant':
      return {
        ...result,
        scientificName: getScientificName(result.name),
        careInfo: "Water weekly, place in partial sunlight.",
        toxicity: Math.random() > 0.7 ? "Toxic to pets" : "Non-toxic"
      };
    case 'animal':
      return {
        ...result,
        species: getSpecies(result.name),
        breed: getBreed(result.name),
        behavior: "Typically docile and friendly."
      };
    case 'landmark':
      return {
        ...result,
        location: "Coordinates: 40.7128° N, 74.0060° W",
        history: "Built in the early 20th century, this landmark has historical significance.",
        nearbyInfo: "Several restaurants and shops within walking distance."
      };
    default:
      return result as ProductDetails;
  }
};

function getMockName(category: ItemCategory): string {
  switch (category) {
    case 'product':
      const products = ["Wireless Headphones", "Smart Watch", "Coffee Maker", "Desk Lamp", "Bluetooth Speaker", "Yoga Mat", "Water Bottle", "Backpack"];
      return products[Math.floor(Math.random() * products.length)];
    case 'plant':
      const plants = ["Monstera Deliciosa", "Snake Plant", "Fiddle Leaf Fig", "Pothos", "Peace Lily", "Aloe Vera", "Spider Plant", "ZZ Plant"];
      return plants[Math.floor(Math.random() * plants.length)];
    case 'animal':
      const animals = ["Golden Retriever", "Siamese Cat", "Red-tailed Hawk", "Monarch Butterfly", "Red Fox", "American Bison", "Bottlenose Dolphin"];
      return animals[Math.floor(Math.random() * animals.length)];
    case 'landmark':
      const landmarks = ["Eiffel Tower", "Statue of Liberty", "Golden Gate Bridge", "Colosseum", "Taj Mahal", "Great Wall", "Machu Picchu"];
      return landmarks[Math.floor(Math.random() * landmarks.length)];
    default:
      return "Unknown Object";
  }
}

function getMockDescription(category: ItemCategory): string {
  switch (category) {
    case 'product':
      return "A popular consumer product known for its quality and features. Highly rated by users.";
    case 'plant':
      return "A common houseplant that's relatively easy to care for and adds greenery to indoor spaces.";
    case 'animal':
      return "A species found in various habitats. Known for its distinctive appearance and behaviors.";
    case 'landmark':
      return "A famous landmark visited by millions of tourists annually. Known for its architectural significance.";
    default:
      return "An unidentified object.";
  }
}

function getScientificName(name: string): string {
  const latinPrefixes = ["Botanicus", "Floris", "Verdantus", "Folium"];
  const latinSuffixes = ["majorus", "minimus", "exoticus", "domestica"];
  
  return `${latinPrefixes[Math.floor(Math.random() * latinPrefixes.length)]} ${latinSuffixes[Math.floor(Math.random() * latinSuffixes.length)]}`;
}

function getSpecies(name: string): string {
  if (name.includes("Dog") || name.includes("Retriever")) return "Canis familiaris";
  if (name.includes("Cat")) return "Felis catus";
  if (name.includes("Hawk")) return "Buteo jamaicensis";
  if (name.includes("Butterfly")) return "Danaus plexippus";
  if (name.includes("Fox")) return "Vulpes vulpes";
  if (name.includes("Bison")) return "Bison bison";
  if (name.includes("Dolphin")) return "Tursiops truncatus";
  return "Animalia unknown";
}

function getBreed(name: string): string {
  if (name.includes("Retriever")) return "Golden Retriever";
  if (name.includes("Cat")) return "Siamese";
  return "Unknown breed";
}

export const MOCK_HISTORY: ScanResult[] = [
  {
    id: "abc123",
    name: "Sony WH-1000XM4",
    category: "product",
    description: "Premium noise-cancelling headphones with excellent sound quality and battery life.",
    confidence: 0.95,
    imageUri: "https://images.unsplash.com/photo-1546435770-a3e426bf472b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1165&q=80",
    timestamp: Date.now() - 86400000, // 1 day ago
    saved: true
  },
  {
    id: "def456",
    name: "Monstera Deliciosa",
    category: "plant",
    description: "A popular houseplant known for its distinctive split leaves and tropical appearance.",
    confidence: 0.88,
    imageUri: "https://images.unsplash.com/photo-1614594975525-e45190c55d0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80",
    timestamp: Date.now() - 172800000, // 2 days ago
    saved: false
  },
  {
    id: "ghi789",
    name: "Golden Retriever",
    category: "animal",
    description: "A friendly, intelligent dog breed known for its golden coat and gentle temperament.",
    confidence: 0.92,
    imageUri: "https://images.unsplash.com/photo-1552053831-71594a27632d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=662&q=80",
    timestamp: Date.now() - 259200000, // 3 days ago
    saved: true
  },
  {
    id: "jkl012",
    name: "Eiffel Tower",
    category: "landmark",
    description: "An iconic iron tower located in Paris, France, and one of the most recognizable structures in the world.",
    confidence: 0.97,
    imageUri: "https://images.unsplash.com/photo-1543349689-9a4d426bee8e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1101&q=80",
    timestamp: Date.now() - 345600000, // 4 days ago
    saved: false
  }
];