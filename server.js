const http = require('http');
const os = require('os');

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();
const port = 19000;

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Expo Development Server</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .qr-section { background: #f5f5f5; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .metro-info { background: #e3f2fd; padding: 15px; border-radius: 5px; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
      </style>
    </head>
    <body>
      <h1>📱 Expo Development Server</h1>
      
      <div class="metro-info">
        <h3>✅ Metro Bundler is Running</h3>
        <p>Your React Native bundler is active on:</p>
        <ul>
          <li><strong>Port 8081:</strong> <code>http://${localIP}:8081</code></li>
          <li><strong>Port 3000:</strong> <code>http://${localIP}:3000</code></li>
        </ul>
      </div>

      <div class="qr-section">
        <h3>📲 Connect with Expo Go App</h3>
        <ol>
          <li><strong>Download Expo Go</strong> from your app store</li>
          <li><strong>Make sure your phone and computer are on the same WiFi network</strong></li>
          <li><strong>In Expo Go, manually enter this URL:</strong></li>
        </ol>
        
        <h4>📍 Connection URLs:</h4>
        <p><strong>Primary:</strong> <code>exp://${localIP}:8081</code></p>
        <p><strong>Alternative:</strong> <code>exp://${localIP}:3000</code></p>
        
        <h4>🌐 Or try these in Expo Go:</h4>
        <p><strong>HTTP:</strong> <code>http://${localIP}:8081</code></p>
        <p><strong>HTTP Alt:</strong> <code>http://${localIP}:3000</code></p>
      </div>

      <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <h4>💡 Troubleshooting:</h4>
        <ul>
          <li>Make sure both devices are on the same WiFi network</li>
          <li>Your local IP address is: <strong>${localIP}</strong></li>
          <li>Try replacing the IP with your actual computer's IP if this doesn't work</li>
          <li>Check your firewall settings if connection fails</li>
        </ul>
      </div>

      <script>
        setInterval(() => {
          document.getElementById('status').textContent = new Date().toLocaleTimeString();
        }, 1000);
      </script>
      
      <p><small>Status: <span id="status">${new Date().toLocaleTimeString()}</span> - Server running on port ${port}</small></p>
    </body>
    </html>
  `);
});

server.listen(port, () => {
  console.log(\`🚀 Expo Development Server running on http://localhost:\${port}\`);
  console.log(\`📱 Connect Expo Go to: exp://\${localIP}:8081\`);
  console.log(\`🌐 View in browser: http://localhost:\${port}\`);
});