import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { Image } from 'expo-image';
import { Bookmark, BookmarkCheck, Share2 } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { ScanResult } from '@/types/scan';

interface ResultCardProps {
  result: ScanResult;
  onToggleSave: (id: string) => void;
  onPress?: () => void;
  compact?: boolean;
}

// We can use dimensions for responsive design if needed

const ResultCard: React.FC<ResultCardProps> = ({ 
  result, 
  onToggleSave, 
  onPress,
  compact = false
}) => {
  const { name, category, confidence, imageUri, saved } = result;
  
  const confidencePercent = Math.round(confidence * 100);
  
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'product': return Colors.primary;
      case 'plant': return Colors.success;
      case 'animal': return Colors.warning;
      case 'landmark': return Colors.accent;
      default: return Colors.textSecondary;
    }
  };

  const categoryColor = getCategoryColor(category);
  
  if (compact) {
    return (
      <TouchableOpacity 
        style={styles.compactContainer}
        onPress={onPress}
        testID={`result-card-${result.id}`}
      >
        <Image 
          source={{ uri: imageUri }}
          style={styles.compactImage}
          contentFit="cover"
          transition={200}
        />
        <View style={styles.compactContent}>
          <Text style={styles.compactName} numberOfLines={1}>{name}</Text>
          <View style={styles.compactMeta}>
            <View style={[styles.categoryBadge, { backgroundColor: categoryColor }]}>
              <Text style={styles.categoryText}>{category}</Text>
            </View>
            <Text style={styles.confidenceText}>{confidencePercent}%</Text>
          </View>
        </View>
        <TouchableOpacity 
          style={styles.compactBookmark}
          onPress={() => onToggleSave(result.id)}
        >
          {saved ? (
            <BookmarkCheck size={20} color={Colors.primary} />
          ) : (
            <Bookmark size={20} color={Colors.inactive} />
          )}
        </TouchableOpacity>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container} testID={`result-card-${result.id}`}>
      <Image 
        source={{ uri: imageUri }}
        style={styles.image}
        contentFit="cover"
        transition={300}
      />
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{name}</Text>
          <View style={[styles.categoryBadge, { backgroundColor: categoryColor }]}>
            <Text style={styles.categoryText}>{category}</Text>
          </View>
        </View>
        
        <View style={styles.confidenceBar}>
          <View 
            style={[
              styles.confidenceFill, 
              { width: `${confidencePercent}%`, backgroundColor: categoryColor }
            ]} 
          />
          <Text style={styles.confidenceLabel}>
            Confidence: <Text style={styles.confidenceValue}>{confidencePercent}%</Text>
          </Text>
        </View>
        
        <View style={styles.actions}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.saveButton]} 
            onPress={() => onToggleSave(result.id)}
          >
            {saved ? (
              <BookmarkCheck size={20} color={Colors.primary} />
            ) : (
              <Bookmark size={20} color={Colors.primary} />
            )}
            <Text style={styles.actionText}>{saved ? 'Saved' : 'Save'}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, styles.shareButton]}>
            <Share2 size={20} color={Colors.accent} />
            <Text style={[styles.actionText, { color: Colors.accent }]}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    overflow: 'hidden',
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  image: {
    width: '100%',
    height: 200,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  name: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
  },
  categoryBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  categoryText: {
    color: Colors.card,
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  confidenceBar: {
    height: 24,
    backgroundColor: Colors.border,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    position: 'relative',
  },
  confidenceFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    borderRadius: 12,
  },
  confidenceLabel: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text,
  },
  confidenceValue: {
    fontWeight: '700',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  saveButton: {
    backgroundColor: Colors.highlight,
    marginRight: 8,
  },
  shareButton: {
    backgroundColor: '#F5F3FF',
    marginLeft: 8,
  },
  actionText: {
    marginLeft: 8,
    fontWeight: '500',
    color: Colors.primary,
  },
  // Compact styles
  compactContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    borderRadius: 12,
    overflow: 'hidden',
    marginHorizontal: 16,
    marginVertical: 6,
    height: 80,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  compactImage: {
    width: 80,
    height: 80,
  },
  compactContent: {
    flex: 1,
    paddingHorizontal: 12,
  },
  compactName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  compactMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactBookmark: {
    padding: 12,
  },
  confidenceText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
});

export default ResultCard;