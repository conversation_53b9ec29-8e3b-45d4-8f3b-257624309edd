import React, { useEffect, useState } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { useScanStore } from '@/hooks/useScanStore';
import DetailedResult from '@/components/DetailedResult';
import EmptyState from '@/components/EmptyState';
import { AnimalDetails, LandmarkDetails, PlantDetails, ProductDetails } from '@/types/scan';
import { getDetailedResult } from '@/constants/mockData';

export default function DetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { history, toggleSaved } = useScanStore();
  const [detailedItem, setDetailedItem] = useState<ProductDetails | PlantDetails | AnimalDetails | LandmarkDetails | null>(null);

  useEffect(() => {
    if (id) {
      console.log('Looking for item with id:', id);
      console.log('Available history items:', history.map(h => ({ id: h.id, name: h.name })));
      
      const item = history.find(h => h.id === id);
      if (item) {
        console.log('Found item:', item.name);
        const detailed = getDetailedResult(item);
        console.log('Generated detailed result:', detailed);
        setDetailedItem(detailed);
      } else {
        console.log('Item not found in history');
        setDetailedItem(null);
      }
    }
  }, [id, history]);

  const handleBack = () => {
    router.back();
  };

  return (
    <>
      <Stack.Screen 
        options={{
          title: detailedItem?.name || 'Details',
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.headerButton}>
              <ArrowLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <View style={styles.container}>
        {detailedItem ? (
          <DetailedResult 
            result={detailedItem}
            onToggleSave={toggleSaved}
          />
        ) : (
          <EmptyState 
            title="Item not found"
            message="The requested item could not be found in your history"
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerButton: {
    padding: 8,
  },
});