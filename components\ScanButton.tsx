import React from 'react';
import { StyleSheet, TouchableOpacity, View, ActivityIndicator } from 'react-native';
import { Camera, ScanBarcode } from 'lucide-react-native';
import { Colors } from '@/constants/colors';

interface ScanButtonProps {
  onPress: () => void;
  isProcessing: boolean;
  isScanMode: boolean;
}

const ScanButton: React.FC<ScanButtonProps> = ({ onPress, isProcessing, isScanMode }) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      disabled={isProcessing}
      testID="scan-button"
    >
      <View style={styles.button}>
        {isProcessing ? (
          <ActivityIndicator size="large" color={Colors.background} />
        ) : (
          isScanMode ? (
            <Camera size={32} color={Colors.background} />
          ) : (
            <ScanBarcode size={32} color={Colors.background} />
          )
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 30,
    alignSelf: 'center',
  },
  button: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default ScanButton;